/**
 * WhatsApp Integration Utility
 * 
 * Handles WhatsApp consultation functionality across the application
 */

document.addEventListener('DOMContentLoaded', function() {
    // WhatsApp configuration
    const WHATSAPP_CONFIG = {
        number: '6285189523863', // WhatsApp number without + and spaces
        baseUrl: 'https://wa.me/',
        defaultMessage: 'Halo! Saya tertarik untuk konsultasi mengenai jasa arsitektur. Mohon informasi lebih lanjut.'
    };

    /**
     * Generate WhatsApp URL with custom message
     * @param {string} message - Custom message to send
     * @returns {string} - Complete WhatsApp URL
     */
    function generateWhatsAppUrl(message = WHATSAPP_CONFIG.defaultMessage) {
        return `${WHATSAPP_CONFIG.baseUrl}${WHATSAPP_CONFIG.number}?text=${encodeURIComponent(message)}`;
    }

    /**
     * Open WhatsApp with custom message
     * @param {string} message - Custom message to send
     */
    function openWhatsApp(message = WHATSAPP_CONFIG.defaultMessage) {
        const url = generateWhatsAppUrl(message);
        window.open(url, '_blank', 'noopener,noreferrer');
    }

    /**
     * Initialize WhatsApp consultation buttons
     */
    function initializeWhatsAppButtons() {
        // Find all consultation buttons that should redirect to WhatsApp
        const consultationButtons = document.querySelectorAll('[data-whatsapp-consultation]');
        
        consultationButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const customMessage = this.getAttribute('data-whatsapp-message') || WHATSAPP_CONFIG.defaultMessage;
                openWhatsApp(customMessage);
                
                // Analytics tracking (if needed)
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'whatsapp_consultation_click', {
                        'event_category': 'engagement',
                        'event_label': 'consultation_button'
                    });
                }
            });
        });
    }

    /**
     * Add floating WhatsApp button (optional)
     */
    function addFloatingWhatsAppButton() {
        // Check if floating button should be added
        if (document.querySelector('.floating-whatsapp')) return;

        const floatingButton = document.createElement('div');
        floatingButton.className = 'floating-whatsapp fixed bottom-6 right-6 z-50';
        floatingButton.innerHTML = `
            <a href="${generateWhatsAppUrl()}" 
               target="_blank" 
               rel="noopener noreferrer"
               class="group flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
               aria-label="Konsultasi via WhatsApp">
                <i class="fab fa-whatsapp text-2xl group-hover:scale-110 transition-transform"></i>
            </a>
        `;

        document.body.appendChild(floatingButton);

        // Add entrance animation
        setTimeout(() => {
            floatingButton.style.transform = 'translateY(0)';
            floatingButton.style.opacity = '1';
        }, 1000);
    }

    /**
     * Handle contact form submission to WhatsApp
     */
    function handleContactFormToWhatsApp() {
        const contactForm = document.getElementById('contact-form-element');
        if (!contactForm) return;

        // Add WhatsApp option to contact form
        const submitButton = contactForm.querySelector('button[type="submit"]');
        if (submitButton) {
            const whatsappButton = document.createElement('button');
            whatsappButton.type = 'button';
            whatsappButton.className = 'w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2 mt-3';
            whatsappButton.innerHTML = `
                <i class="fab fa-whatsapp"></i>
                <span>Konsultasi via WhatsApp</span>
            `;

            whatsappButton.addEventListener('click', function() {
                const formData = new FormData(contactForm);
                const name = formData.get('name') || '';
                const phone = formData.get('phone') || '';
                const message = formData.get('message') || '';

                let whatsappMessage = 'Halo! Saya ingin konsultasi mengenai jasa arsitektur.\n\n';
                if (name) whatsappMessage += `Nama: ${name}\n`;
                if (phone) whatsappMessage += `Telepon: ${phone}\n`;
                if (message) whatsappMessage += `Pesan: ${message}`;

                openWhatsApp(whatsappMessage);
            });

            submitButton.parentNode.insertBefore(whatsappButton, submitButton.nextSibling);
        }
    }

    // Initialize all WhatsApp functionality
    initializeWhatsAppButtons();
    handleContactFormToWhatsApp();

    // Add floating WhatsApp button for better accessibility
    addFloatingWhatsAppButton();

    // Export functions for global use
    window.WhatsAppUtils = {
        generateUrl: generateWhatsAppUrl,
        open: openWhatsApp,
        config: WHATSAPP_CONFIG
    };
});

/**
 * CSS for floating WhatsApp button animation
 */
const whatsappStyles = document.createElement('style');
whatsappStyles.textContent = `
    .floating-whatsapp {
        transform: translateY(100px);
        opacity: 0;
        transition: all 0.5s ease-out;
    }
    
    .floating-whatsapp:hover {
        transform: translateY(0) scale(1.1);
    }
    
    @media (max-width: 768px) {
        .floating-whatsapp {
            bottom: 1rem;
            right: 1rem;
        }
    }
`;
document.head.appendChild(whatsappStyles);
